import { ContentType, MessageType, MessageTypeEnum } from '@src/types/ai-chat';
import React, { useEffect, useState, useMemo } from 'react';
import {
  ChatContent,
  ChatItem,
  ChatReasoning,
  ChatMarkdown,
} from '@tencent/cloud-chat-ui';
import TemplatePreviewPanel from '../template-preview-panel';
import { t } from '@tea/app/i18n';
import { TEMPLATE_MOCK, TEMPLATE_QUERY_MOCK } from '@src/pages/guard-agent-chat/contants';
import './index.less';
import { isEmpty, find } from 'lodash';

interface MessageItemProps {
  item: MessageType;
  processing?: boolean;
  messages?: MessageType[];
  onMessageChange?: (messages: MessageType[]) => void;
  handleSendMessage?: (message: string, options?: {
    addLast?: boolean;
    error?: string;
  }) => void;
}

enum ReasoningStatus {
  loading = 'loading',
  stopped = 'stopped',
  finished = 'finished'
}

const StatusTextMap = {
  [ReasoningStatus.loading]: t('思考中...'),
  [ReasoningStatus.stopped]: t('思考已终止'),
  [ReasoningStatus.finished]: t('思考完成'),
};

// 单个消息组件
const MessageItem = (props: MessageItemProps) => {
  const {
    item,
    processing,
    messages,
    onMessageChange,
  } = props;
  const isUser = item.type === MessageTypeEnum.user;
  const { steps, deepThink, content, id, costTime = 0 } = item;
  const [count, setCount] = useState(costTime);
  const [isExpanded, setIsExpanded] = useState(true);
  const [reasoningStatus, setReasoningStatus] = useState(ReasoningStatus.finished);
  const template = find(item?.steps, item => (
    item.stepKey === TEMPLATE_MOCK || item.stepKey === TEMPLATE_QUERY_MOCK
  )) || {};

  const perviewDetail = useMemo(() => {
    if (isEmpty(template)) return {};
    return template?.stepContent;
  }, [template]);

  useEffect(() => {
    if (!content && (deepThink || !isEmpty(steps))) {
      setReasoningStatus(ReasoningStatus.loading);
      setIsExpanded(true);
    }
    if (content) {
      setReasoningStatus(ReasoningStatus.finished);
      setIsExpanded(false);
    }
  }, [content, steps, deepThink]);

  useEffect(() => {
    let timer;
    if (reasoningStatus === ReasoningStatus.loading && processing) {
      timer = setInterval(() => {
        setCount(prev => prev + 1);
      }, 1000);
    }
    if (reasoningStatus === ReasoningStatus.loading && !processing) {
      setReasoningStatus(ReasoningStatus.stopped);
    }
    return () => timer && clearInterval(timer);
  }, [reasoningStatus, processing]);

  useEffect(() => {
    onMessageChange?.([...(messages.slice(0, messages.length - 1)), {
      id: messages[messages.length - 1]?.id,
      type: MessageTypeEnum.assistant,
      contentType: ContentType.text,
      steps: messages[messages.length - 1]?.steps,
      deepThink: messages[messages.length - 1]?.deepThink,
      content: messages[messages.length - 1]?.content,
      costTime: count,
    }]);
  }, [count]);

  const renderContent = (item: MessageType) => {
    switch (item.contentType) {
      case ContentType.text: {
        if (isUser) {
          return <ChatItem role='user' className='message-user-item'>
            <ChatContent content={item.content} type='text'/>
          </ChatItem>;
        }
        return (
          <>
            {
              deepThink
              && <ChatReasoning
                isExpanded={isExpanded}
                statusIcon={reasoningStatus}
                statusText={`${StatusTextMap[reasoningStatus]}${perviewDetail?.MockTemplateContent ? t('，已为你设置播报任务') : ''}`}
                time={`${count}S`}
              >
                <ChatMarkdown content={deepThink} />
              </ChatReasoning>
            }
            {
              content
              && <ChatItem
                role='assistant'
                className='message-assistant-item'
              >
                <ChatContent content={content} type='text' />
              </ChatItem>
            }
            {
              perviewDetail?.MockTemplateContent
              && <ChatItem role='assistant' className='message-assistant-item'>
                <TemplatePreviewPanel
                  perviewDetail={perviewDetail}
                  stepKey={template?.stepKey ?? ''}
                  id={id}
                />
              </ChatItem>
            }
          </>
        );
      }
      case ContentType.default:
        return <div>
          {item?.content?.message ?? t('正在处理中...')}
          </div>;
      default:
        return null;
    }
  };

  return <>
    {renderContent(item)}
  </>;
};

export default MessageItem;
