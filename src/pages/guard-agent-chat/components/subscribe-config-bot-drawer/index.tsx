import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Chat<PERSON>ab<PERSON>, ChatInstructionList } from '@tencent/cloud-chat-ui';
import { Input, Icon, Bubble, message } from '@tencent/tea-component';
import { t } from '@tea/app/i18n';
import { describeGuardAISubscribeMetricInfo } from '@src/service/api/broadcast-agent-chat';
import BroadcastList from './broadcast-list';
import './index.less';

interface Props {
  visible: boolean,
  onClose: () => void,
  inputValue: string,
  setInputValue: (val: string) => void,
}

enum TitleTabsEnum {
  template = 'template',
  mySubscribe = 'mySubscribe',
}


const SubscribeConfigBotDrawer = ({ visible, onClose, inputValue, setInputValue }: Props) => {
  const [titleTabs] = useState([
    { id: TitleTabsEnum.template, label: t('播报指标') },
    { id: TitleTabsEnum.template, label: t('我的订阅') },
  ]);
  const [contentTabs, setContentTabs] = useState([]);
  const [tabsDataMap, setTabsDataMap] = useState({});
  const [searchValue, setSearchValue] = useState('');
  const [list, setList] = useState([]);
  const [activeId, setActiveId] = useState('');
  const getKeyword = (val, searchVal) => {
    const valLoawerCase = val?.toLowerCase();
    const searchValLoawerCase = searchVal?.toLowerCase();
    const satrtIndex = valLoawerCase.indexOf(searchValLoawerCase);
    return val.slice(satrtIndex, satrtIndex + (searchVal?.length ?? 0));
  };
  const getDescribeGuardAISubscribeMetricInfo = async () => {
    const res: any = await describeGuardAISubscribeMetricInfo();
    if (res.Error) {
      const msg = res.Error.Message;
      message.error({ content: msg });
      return;
    }
    const temTabs = [];
    const temMap = {};
    const temList = [];
    res?.ProductMetricInfoList?.forEach((item) => {
      temTabs.push({
        id: item.Product,
        label: item.ProductName,
      });
      temMap[item.Product] = item.MetricInfoList;
      item.MetricInfoList?.forEach((el) => {
        temList.push({
          id: item.Product,
          productName: item.ProductName,
          metricInfo: el,
        });
      });
    });
    setActiveId(temTabs?.[0]?.id ?? '');
    setContentTabs(temTabs);
    setTabsDataMap(temMap);
    setList(temList);
  };
  useEffect(() => {
    if (visible) {
      getDescribeGuardAISubscribeMetricInfo();
    }
  }, [visible]);
  useEffect(() => {
    getDescribeGuardAISubscribeMetricInfo();
  }, []);

  return <ChatDrawer
    className={'sub-config-drawer-wrap'}
    visible={visible}
    destroyOnClose={false}
    outerClickClosable
    placement='bottom'
    onClose={() => onClose?.()}
    title={
      <div className={'search-wrap'}>
        <Icon type="search" />
        <Input
          placeholder={t('请填写指标进行搜索')}
          value={searchValue}
          onChange={
            (val) => {
              setSearchValue(val);
            }
          }
          size={'full'}
        />
        {
          searchValue && <div
            className={'close-icon-wrap'}
            onClick={
              () => {
                setSearchValue('');
              }
            }
          >
            <Icon type="close" />
          </div>
        }
      </div>
    }
  >
    {searchValue ? (
      <div>
        <ChatInstructionList>
          {list
            .filter(item => (item?.metricInfo?.MetricName ?? '')?.toLowerCase()?.includes(searchValue?.toLowerCase()))
            .map((item, index) => <Bubble content={item?.metricInfo.MetricName ?? ''} key={index} openDelay={700}>
                <ChatInstructionList.Item
                  keyword={getKeyword(item?.metricInfo?.MetricName ?? '', searchValue)}
                  label={item?.metricInfo.MetricName ?? ''}
                  category={item.productName ?? ''}
                  showCollect={false}
                  onClick={
                    () => {
                      setInputValue(`${inputValue}${inputValue && '\n'}${item.id} ${item?.metricInfo.MetricName}`);
                      onClose?.();
                    }
                  }
                />
              </Bubble>)}
        </ChatInstructionList>
      </div>
    ) : (
      <ChatTabs tabs={titleTabs} className={'title-tabs-wrap'}>
        <ChatTabs.TabPanel id={TitleTabsEnum.template}>
          <BroadcastList
            contentTabs={contentTabs}
            activeId={activeId}
            setActiveId={setActiveId}
            tabsDataMap={tabsDataMap}
            inputValue={inputValue}
            setInputValue={setInputValue}
            onClose={onClose}
          />
        </ChatTabs.TabPanel>
      </ChatTabs>
    )}
  </ChatDrawer>;
};

export default SubscribeConfigBotDrawer;
