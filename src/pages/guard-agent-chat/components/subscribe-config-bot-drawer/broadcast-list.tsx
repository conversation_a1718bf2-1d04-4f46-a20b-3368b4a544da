import React from 'react';
import { ChatTabs, ChatInstructionList } from '@tencent/cloud-chat-ui';
import { Bubble, StatusTip } from '@tencent/tea-component';

interface BroadcastListProps {
  contentTabs: Array<{
    id: string;
    label: string;
  }>;
  activeId: string;
  setActiveId: (id: string) => void;
  tabsDataMap: Record<string, Array<{
    MetricName: string;
    [key: string]: any;
  }>>;
  inputValue: string;
  setInputValue: (value: string) => void;
  onClose?: () => void;
}

const BroadcastList: React.FC<BroadcastListProps> = ({
  contentTabs,
  activeId,
  setActiveId,
  tabsDataMap,
  inputValue,
  setInputValue,
  onClose
}) => {
  return (
    <>
      {
        contentTabs?.length > 0 ? (
          <ChatTabs
            placement='left'
            tabs={contentTabs}
            activeId={activeId}
            tabBarRender={
              (children, tab) => {
                return (
                  <Bubble className={'sub-config-tab-l-bubble'} content={tab.label ?? ''} openDelay={700}>
                    <a
                      className={`tea-tabs__tab ${tab.id === activeId ? 'is-active' : ''}`}
                      onClick={
                        () => {
                          setActiveId(tab.id);
                        }
                      }
                    >
                      {children}
                    </a>
                  </Bubble>
                );
              }
            }
          >
            {
              contentTabs?.map((item, i) => (
                <ChatTabs.TabPanel id={item.id} key={i}>
                  <ChatInstructionList>
                    {tabsDataMap?.[item.id]?.map((el, index) => (
                      <Bubble content={el.MetricName ?? ''} key={index} openDelay={700}>
                        <ChatInstructionList.Item
                          label={el.MetricName}
                          key={index}
                          showCollect={false}
                          onClick={
                            () => {
                              setInputValue(`${inputValue}${inputValue && '\n'}${item.id} ${el.MetricName}`);
                              onClose?.();
                            }
                          }
                        />
                      </Bubble>
                    ))}
                  </ChatInstructionList>
                </ChatTabs.TabPanel>
              ))
            }
          </ChatTabs>
        ) : (
          <div className="status-wrap">
            <StatusTip status={'empty'} />
          </div>
        )
      }
    </>
  );
};

export default BroadcastList;
