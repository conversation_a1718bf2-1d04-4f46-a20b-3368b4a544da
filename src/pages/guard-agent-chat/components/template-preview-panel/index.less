.charts-card {
  min-width: 350px;
  box-sizing: border-box;
  padding-right: 20px;

  .charts-card-perview {
    white-space: pre-wrap;
    color: #000;
    font-size: 13px;
    line-height: 28px;
    margin-bottom: 12px;
    border-bottom: 1px dashed #ddd;
    
    .charts-card-perview-tip {
      margin: 14px 0 6px 0;
      font-weight: bold;
    }
    .tea-tag {
      margin: 0;
    }
  }
  .charts-card-action {
    .charts-card-form {
      display: flex;
    }

    .charts-card-action-btn {
      display: flex;
      padding-top: 6px;
    }
  }
}

.common {
  .markdown {
    overflow: auto;
  
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin-bottom: 1em;
  
      &:last-child {
        margin-bottom: 0;
      }
    }
  
    /* 段落样式 */
    p {
      margin-bottom: 1em;
      line-height: 1.6;
  
      &:last-child {
        margin-bottom: 0;
      }
    }
  
    ul {
      list-style-type: disc;
    }
  
    ol {
      list-style-type: decimal;
    }
  
    /* 列表样式 */
    ul,
    ol {
      margin-bottom: 1em;
      margin-left: 1.5em;
    }
  
    li {
      margin-bottom: .5em;
    }
  
    details > summary {
      color: #888;
    }
  
    th,
    td {
      padding: 8px !important;
      border: 1px solid #bcb9b9;
      border-top: 0;
      border-right: 0; /* 移除右边框 */
      border-left: 0; /* 移除左边框 */
      text-align: left;
    }
    
    th {
      background-color: #f8f8f8;
      font-weight: bold;
    }
  
    table {
      overflow: auto;
      width: 100%;
      max-height: 400px;
      margin-bottom: 10px;
      border-collapse: separate;
      border-spacing: 0;
  
      thead {
        position: sticky;
        top: 0;
  
        th {
          border-top: 0;
        }
      }
  
      tbody {
        tr {
          table-layout: fixed;
        }
      }
    }
  
    pre {
      div {
        overflow: unset !important;
      }
  
      code {
        display: block;
        white-space: pre-wrap !important;
        word-break: break-all !important;
        word-wrap: break-word;
      }
    }
  
    code {
      padding: 2px 4px;
      border: 0;
      border-radius: 4px;
      background-color: #f3f3f3;
      color: rgba(0, 0, 0, .9);
    }
  }

  .mask {
    position: absolute;
    z-index: 1000;
    top: 0;
    left: 0;
    display: flex;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, .5);
  }


  :global {
    .sdk-ai-bi-tabs {
      height: 100%;
    }

    .sdk-ai-bi-tabs__tab {
      &:hover {
        text-decoration: none;
      }
    }

    .sdk-ai-bi-tabs__tabpanel {
      overflow: hidden;
      padding: 0;
    }

    .sdk-ai-bi-card__content {
      position: relative;
    }
  }
}

.bi {
  :global {
    .sdk-ai-bi-tabs__tabpanel {
      overflow: auto;
      height: calc(100% - 40px);
      max-height: 400px;
    }
  }
}

