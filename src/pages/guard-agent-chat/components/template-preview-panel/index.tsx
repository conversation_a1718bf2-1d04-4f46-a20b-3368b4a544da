import {
  Select, Form, message, Input,
} from '@tencent/tea-component';
import React, { useEffect, useState } from 'react';
import { map, set, cloneDeep, isEmpty, find } from 'lodash';
import { Tag, Button } from 'tdesign-react';
import { useForm, Controller } from 'react-hook-form';
import originStore from '@src/origin-store/store';
import { useDispatch } from 'react-redux';
import { TEMPLATE_QUERY_MOCK } from '@src/pages/guard-agent-chat/contants';
import { getGuardSubscribeChannel, saveBroadcastTemplate, disableBroadcast } from '@src/service/api/broadcast-agent-chat';
import { useCommonSelector, changeCommonData } from '@src/store/app-common';
import './index.less';
import { t } from '@tea/app/i18n';

enum ChatChannelType {
  afterSale = 1,
  guardChat = 2,
  webhook = 3,
  em = 4,
}

enum WebHookType {
  WEBHOOK = 'webhook',
  EM = 'em',
}

const webhookList = ['webhook', 'em'];
interface IProps {
  perviewDetail: any;
  id: string;
  stepKey: string;
}
const TemplatePreviewPanel = ({ perviewDetail, stepKey, id }: IProps) => {
  const { MockTemplateContent = '', BroadcastCycle } = perviewDetail;
  const { graphApi: apis }  = originStore.getState().guard;
  const { chatTemplateInfo, guardInfoDetail, taskResult }  = useCommonSelector();
  const {
    control,
    watch,
    setValue,
    clearErrors,
    handleSubmit,
    formState: { errors },
  } = useForm({ mode: 'all' });

  const dispatch = useDispatch();

  const [broadcastTypeOption, setBroadcastTypeOption] = useState([]);
  const [broadcastRateOption, setBroadcastRateOption] = useState([]);
  const [btnDisabled, setBtnDisabled] = useState(false);
  const [isloading, setIsloading] = useState(false);
  const watchChatGroupId = watch('ChatGroupId');
  const watchBroadcastCycle = watch('BroadcastCycle');

  const getStatus = (fieldState) => {
    if (fieldState?.error?.message) {
      return 'error';
    }
    if (!fieldState.isDirty) {
      return undefined;
    }
    return fieldState.invalid ? 'error' : 'success';
  };

  const getBroadcastChannelOption = () => {
    getGuardSubscribeChannel({
      GuardId: guardInfoDetail?.Status > 1 ? guardInfoDetail.GuardId : 0,
      // MockAppid: 1251524319,
      // MockUin: '2905526316',
    })
      .then((res: any) => {
        const list = map(res?.ChannelList, item => ({
          text: item.ChannelName,
          value: item.ChannelId,
          disabled: false,
        }));
        list.push(
          { text: t('自定义WebHook'), value: WebHookType.WEBHOOK, disabled: false },
          { text: t('企业微信'), value: WebHookType.EM, disabled: false }
        );
        const chat = find(list, item => item.value === perviewDetail?.ChatGroupId) || {};
        if (!isEmpty(chat)) {
          setBroadcastTypeOption(list);
        } else if (stepKey === TEMPLATE_QUERY_MOCK) {
          list.push({ text: perviewDetail?.ChatGroupName, value: perviewDetail?.ChatGroupId, disabled: true });
          setBroadcastTypeOption(list);
        } else {
          setBroadcastTypeOption(list);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const stopBroadcast = (values) => {
    setIsloading(true);
    disableBroadcast({ ArchId: apis?.archInfo?.archId })
      .then((res: any) => {
        console.log(res);
        const newChatTemplateInfo = cloneDeep(chatTemplateInfo);
        set(newChatTemplateInfo, id, {
          ...newChatTemplateInfo[id],
          ...values,
          Status: false,
        });

        dispatch(changeCommonData({
          chatTemplateInfo: newChatTemplateInfo,
        }));
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsloading(false);
      });
  };

  const onSubmitHandle = (values) => {
    if (btnDisabled) {
      stopBroadcast(values);
      return;
    }
    const chat = find(broadcastTypeOption, item => item.value === values.ChatGroupId) || {};
    if (!isEmpty(chat)) {
      if (chat?.disabled) {
        message.warning({ content: t('当前播报群组不可用！') });
        return;
      };
      set(perviewDetail, 'ChatGroupName', chat?.text);
    }
    setIsloading(true);
    set(perviewDetail, 'BroadcastCycle.SelectCycle', +values.BroadcastCycle);
    saveBroadcastTemplate({
      ChatGroupId: values.ChatGroupId,
      BroadcastCycle: +values.BroadcastCycle,
      ArchId: apis?.archInfo?.archId,
      // eslint-disable-next-line no-nested-ternary
      BroadcastChannel: webhookList?.includes(values.ChatGroupId)
        ? ChatChannelType[values.ChatGroupId]
        : guardInfoDetail?.Status > 1
          ? ChatChannelType.guardChat
          : ChatChannelType.afterSale,
      StartTime: guardInfoDetail?.Status > 1 ? guardInfoDetail?.StartTime : '',
      EndTime: guardInfoDetail?.Status > 1 ? guardInfoDetail?.EndTime : '',
      TemplateAnalysisData: JSON.stringify(perviewDetail),
      ...(webhookList?.includes(values.ChatGroupId) && { WebHook: values.WebHook }),
    })
      .then((res: any) => {
        if (res?.ErrorMessage) {
          message.error({ content: res?.ErrorMessage });
          return;
        }
        const newChatTemplateInfo = cloneDeep(chatTemplateInfo);
        set(newChatTemplateInfo, id, {
          ...newChatTemplateInfo[id],
          ...values,
          Status: true,
        });
        Object.keys(newChatTemplateInfo).forEach((key) => {
          if (key !== id) {
            // 其他模版设置为不可用
            set(newChatTemplateInfo, `${key}.Status`, false);
          }
        });

        dispatch(changeCommonData({
          chatTemplateInfo: newChatTemplateInfo,
        }));
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsloading(false);
      });
  };

  const parseSpecialTags = (input) => {
    // 同时匹配两种标签格式：$[...] 和 $(...)
    const parts = input.split(/(\$\[.*?\]|\$\(.*?\))/g);

    return parts.map((part, index) => {
      // 匹配方括号标签 $[...]
      const bracketMatch = part.match(/^\$\[(.*)\]$/);
      // 匹配圆括号标签 $(...)
      const parenMatch = part.match(/^\$\((.*)\)$/);

      if (bracketMatch) {
        return (
          <Tag theme="success" variant="outline" key={`bracket-${index}`} >
            {bracketMatch[1]}
          </Tag>
        );
      }
      if (parenMatch) {
        return (
          <Tag theme="danger" variant="light" key={`paren-${index}`} className="dollar-paren-tag">
            {parenMatch[1]}
          </Tag>
        );
      }
      return <React.Fragment key={`text-${index}`}>{part}</React.Fragment>;
    });
  };

  useEffect(() => {
    getBroadcastChannelOption();
  }, [guardInfoDetail]);

  useEffect(() => {
    if (stepKey === TEMPLATE_QUERY_MOCK) {
      if (!perviewDetail?.CanBroadcast) {
        setBtnDisabled(false);
      } else {
        setBtnDisabled(true);
      }
    }
  }, []);

  useEffect(() => {
    if (!isEmpty(BroadcastCycle)) {
      const cycleList  = map(BroadcastCycle?.CycleList, item => ({
        text: item.Name,
        value: item.Value,
      }));
      setBroadcastRateOption(cycleList);
      if (BroadcastCycle?.SelectCycle && !chatTemplateInfo[id]?.BroadcastRate) {
        setValue('BroadcastCycle', BroadcastCycle?.SelectCycle);
      }
    }
  }, [BroadcastCycle, chatTemplateInfo]);

  useEffect(() => {
    if (chatTemplateInfo[id]) {
      setValue('ChatGroupId', chatTemplateInfo[id]?.ChatGroupId);
      setValue('BroadcastCycle', chatTemplateInfo[id]?.BroadcastCycle);
      setValue('WebHook', chatTemplateInfo[id]?.WebHook);
      setBtnDisabled(chatTemplateInfo[id]?.Status);
    } else if (perviewDetail?.ChatGroupId || perviewDetail?.WebHook) {
      setValue('ChatGroupId', perviewDetail?.ChatGroupId);
      setValue('WebHook', perviewDetail?.WebHook);
    } else if (!isEmpty(broadcastTypeOption)) {
      setValue('ChatGroupId', broadcastTypeOption[0]?.value);
    }
  }, [chatTemplateInfo, broadcastTypeOption, perviewDetail]);

  useEffect(() => {
    if (chatTemplateInfo[id]) {
      if (chatTemplateInfo[id]?.ChatGroupId !== watchChatGroupId
        || chatTemplateInfo[id]?.BroadcastCycle !== watchBroadcastCycle
      ) {
        setBtnDisabled(false);
      }
    }
  }, [watchChatGroupId, watchBroadcastCycle, chatTemplateInfo]);

  return <div className='charts-card'>
    <div className='charts-card-perview'>
      {parseSpecialTags(MockTemplateContent)}
      <div className='charts-card-perview-tip'>
        {t('高亮区域为播报动态计算渲染内容')}
      </div>
    </div>
    <div className='charts-card-action'>
      <Form layout="vertical">
        <div className='charts-card-form'>
          <Controller
            name="ChatGroupId"
            control={control}
            rules={{
              validate: value => (!value ? t('请选择播报渠道') : undefined),
            }}
            render={({ field, fieldState }) => (
              <Form.Item
                label={t('播报渠道')}
                status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
                message={errors.ChatGroupId?.message}
              >
                <Select
                  style={{ width: '145px' }}
                  {...field}
                  onChange={(value) => {
                    field.onChange(value);
                    if (!webhookList?.includes(value)) {
                      setValue('WebHook', '');
                      clearErrors('WebHook');
                    }
                  }}
                  searchable
                  matchButtonWidth
                  appearance="button"
                  placeholder={t('请选择播报渠道')}
                  options={broadcastTypeOption}
                />
              </Form.Item>
            )}
          />
          <Controller
            name="BroadcastCycle"
            control={control}
            rules={{
              validate: value => (!value ? t('请选择播报频率') : undefined),
            }}
            render={({ field, fieldState }) => (
              <Form.Item
                label={t('播报频率')}
                status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
                message={errors.BroadcastCycle?.message}
              >
                <Select
                  style={{ width: '145px' }}
                  {...field}
                  searchable
                  matchButtonWidth
                  appearance="button"
                  placeholder={t('请选择播报频率')}
                  options={broadcastRateOption}
                />
              </Form.Item>
            )}
          />
        </div>
        <Controller
          name="WebHook"
          control={control}
          rules={{
            validate: (value) => {
              if (!webhookList?.includes(value)) {
                return undefined;
              }
              if (!value) {
                return t('请输入WebHook地址');
              }
              if (!/^https?:\/\/[^\s]+$/.test(value)) {
                return t('WebHook地址格式不正确');
              }
              return undefined;
            },
          }}
          render={({ field, fieldState }) => (
            <Form.Item
              label={t('WebHook地址')}
              tips={
                watchChatGroupId === WebHookType.WEBHOOK
                  ? <p>
                    <p>{t('接收播报接口请求方式：POST')}</p>
                    <p>{t('入参：{\'Message\': \'播报消息\',\'Type\': \'ping（连通性测试）/ push（正式推送）\'}')}</p>
                    <p>{t('响应状态码200判定为成功。')}</p>
                  </p>
                  : undefined
              }
              status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
              message={errors.WebHook?.message}
            >
              <Input
                style={{ width: '100%' }}
                disabled={!webhookList?.includes(watchChatGroupId)}
                {...field}
                placeholder={t('请输入WebHook地址')}
              />
            </Form.Item>
          )}
        />
      </Form>
      <div className='charts-card-action-btn'>
        <Button
          shape="rectangle"
          size="medium"
          type="button"
          variant="base"
          disabled={taskResult?.ProgressValue !== 1}
          style={{ width: '48%', height: '36px', fontSize: '12px', marginTop: 16 }}
          loading={isloading}
          onClick={handleSubmit(onSubmitHandle) as any}
        >
          {
            btnDisabled
              ? t('停用播报')
              : t('启用播报')
          }
        </Button>
        {/* <Button theme="default" variant="outline">
          修改播报
        </Button> */}
      </div>
    </div>
  </div>;
};

export default TemplatePreviewPanel;
