import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { useSelector } from 'react-redux';
import { guardStatusEnum } from '@src/constants';

interface IAppCommonData {
  guardInfoDetail?: any; // 当前选中防护信息
  archProductInfo?: any; // 护航单资源加入架构图情况
  appId?: number; // 当前appId
  guardStatus?: guardStatusEnum; // 当前防护单状态
  asyncTaskId?: string; // 当前配置更新异步任务id
  taskResult?: any; // 当前配置更新异步任务结果
  allNodeList?: any; // 全部节点数据
  isUserAuth?: boolean; // 是否用户可发起护航
  chatTemplateInfo?: any; // 当前聊天模板信息
  isBroadcastPeriodLimit?: boolean; // 是否播报频率限制
}

const initialState: () => IAppCommonData = () => ({
  guardInfoDetail: {},
  archProductInfo: {},
  appId: 0,
  guardStatus: guardStatusEnum.NO_GUARD_DRAFT,
  asyncTaskId: '',
  taskResult: {},
  allNodeList: [],
  isUserAuth: false,
  chatTemplateInfo: {},
  isBroadcastPeriodLimit: false,
});

export const appSlice = createSlice({
  name: 'appCommon',
  initialState: initialState(),
  reducers: {
    changeCommonData: (state, action: PayloadAction<IAppCommonData>) => {
      Object.assign(state, action.payload);
    },
    resetCommonData: (state) => {
      Object.assign(state, initialState());
    },
  },
});

export const { changeCommonData, resetCommonData } = appSlice.actions;

// 暴露出 appManager 切片数据的 selector
export const useCommonSelector: () => IAppCommonData = () => useSelector((state: any) => state.appCommon);

export default appSlice.reducer;
