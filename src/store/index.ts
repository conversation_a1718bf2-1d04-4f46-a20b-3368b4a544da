import { configureStore, ThunkAction, Action } from '@reduxjs/toolkit';
import { useDispatch } from 'react-redux';
import appManager from './app-manager';
import appCommon from './app-common';

export type RootState = ReturnType<typeof store.getState>;

export type AppDispatch = typeof store.dispatch;

export type AppThunk<ReturnType = void> = ThunkAction<ReturnType, RootState, unknown, Action<string>>;

// 创建 store
export const store = configureStore({
  reducer: {
    appManager,
    appCommon,
  },
});

export { useDispatch };
